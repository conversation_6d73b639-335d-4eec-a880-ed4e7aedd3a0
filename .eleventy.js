const { processFiles } = require('./path-processor');

module.exports = function(eleventyConfig) {
  // Copy static assets directly to output
  eleventyConfig.addPassthroughCopy({
    "src/assets": "assets"
  });
  
  // Copy JS files to dist/js instead of dist/src/js
  eleventyConfig.addPassthroughCopy({
    "src/js": "js",
    "src/hpo": "hpo",
    "src/uretina": "uretina"
  });

  
  // Post-processing function to handle CSS and path fixing
  eleventyConfig.on('eleventy.after', async () => {
    const { execSync } = require('child_process');
    // Run our CSS build and combine steps
    execSync('npm run build:css:prod && npm run combine:css', { stdio: 'inherit' });
    
    // Process paths in HTML files
    processFiles();
    
    // Run minifying steps
    execSync('npm run minify:css && npm run minify:js && npm run minify:html', { stdio: 'inherit' });
  });

  return {
    // Process HTML files through the template engine
    htmlTemplateEngine: "njk",
    
    dir: {
      input: ".",          // Root directory
      output: "dist",      // Output directory (same as current setup)
      includes: "_includes", // For component partials
      layouts: "_includes"   // For layouts (specify same as includes)
    },
    
    // Only process the index.html file
    templateFormats: ["html"]
  };
}; 