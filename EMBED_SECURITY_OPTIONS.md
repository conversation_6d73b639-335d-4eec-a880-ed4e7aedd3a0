# Chat Widget Embed Security Options

This document outlines various methods to restrict access to the `agent-chat-embed.js` script to prevent unauthorized usage.

## 1. Referrer-based Restrictions

Check the HTTP referrer header on your server to only allow specific domains.

### Implementation (Express.js example):
```javascript
app.get('/agent-chat-embed.js', (req, res) => {
    const allowedDomains = ['example.com', 'partner-site.com', 'trusted-domain.org'];
    const referrer = req.get('Referer');
    
    if (referrer && allowedDomains.some(domain => referrer.includes(domain))) {
        res.sendFile(path.join(__dirname, 'agent-chat-embed.js'));
    } else {
        res.status(403).send('Access denied - unauthorized domain');
    }
});
```

### Pros:
- Server-side protection
- Easy to implement
- Can log unauthorized attempts

### Cons:
- Referrer can be spoofed
- Some browsers/extensions block referrer headers

## 2. Domain Validation in the Script

Add domain checking directly inside the embed script to prevent execution on unauthorized domains.

### Implementation:
```javascript
(function() {
    'use strict';
    
    // Domain whitelist
    const allowedDomains = [
        'example.com', 
        'www.example.com',
        'trusted-partner.com',
        'localhost' // for development
    ];
    
    const currentDomain = window.location.hostname;
    
    if (!allowedDomains.includes(currentDomain)) {
        console.warn('Chat widget not authorized for domain:', currentDomain);
        return;
    }
    
    // Rest of your embed code here...
    // ... existing chat widget initialization
})();
```

### Pros:
- Works even if script is cached/copied
- Client-side validation
- Can provide user-friendly error messages

### Cons:
- Can be bypassed by modifying the script
- Domain list is visible in the code

## 3. API Key/Token System

Require an API key that must be included in the script tag.

### HTML Usage:
```html
<script src="https://your-domain.com/agent-chat-embed.js" data-api-key="abc123xyz789"></script>
```

### Implementation:
```javascript
(function() {
    'use strict';
    
    const currentScript = document.currentScript || document.querySelector('script[src*="agent-chat-embed.js"]');
    const apiKey = currentScript?.dataset.apiKey;
    
    // Validate API key (could be server-side validation via AJAX)
    const validApiKeys = ['abc123xyz789', 'def456uvw012']; // In real implementation, validate server-side
    
    if (!apiKey || !validApiKeys.includes(apiKey)) {
        console.error('Invalid or missing API key for chat widget');
        return;
    }
    
    // Rest of your embed code here...
})();
```

### Server-side validation option:
```javascript
// Add this inside the embed script for server-side validation
async function validateApiKey(key) {
    try {
        const response = await fetch('/api/validate-key', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ apiKey: key })
        });
        return response.ok;
    } catch (error) {
        return false;
    }
}
```

### Pros:
- More secure than domain checking alone
- Can track usage per API key
- Can revoke access by disabling keys

### Cons:
- Requires key management system
- More complex implementation

## 4. CORS Headers

Set restrictive Cross-Origin Resource Sharing headers for the script.

### Implementation:
```javascript
// Server-side CORS configuration
app.get('/agent-chat-embed.js', (req, res) => {
    // Only allow specific origins
    const allowedOrigins = [
        'https://example.com',
        'https://www.example.com',
        'https://trusted-partner.com'
    ];
    
    const origin = req.headers.origin;
    
    if (allowedOrigins.includes(origin)) {
        res.setHeader('Access-Control-Allow-Origin', origin);
    }
    
    res.setHeader('Access-Control-Allow-Methods', 'GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    res.sendFile(path.join(__dirname, 'agent-chat-embed.js'));
});
```

### Pros:
- Browser-enforced security
- Standards-based approach

### Cons:
- Only works for cross-origin requests
- May not prevent all unauthorized usage

## 5. IP Whitelisting

Restrict access based on server IP addresses or IP ranges.

### Implementation:
```javascript
const ipRangeCheck = require('ip-range-check'); // npm package

app.get('/agent-chat-embed.js', (req, res) => {
    const allowedIPs = [
        '*************',
        '10.0.0.0/24',  // IP range
        '************'
    ];
    
    const clientIP = req.ip || req.connection.remoteAddress;
    
    if (ipRangeCheck(clientIP, allowedIPs)) {
        res.sendFile(path.join(__dirname, 'agent-chat-embed.js'));
    } else {
        res.status(403).send('IP address not authorized');
    }
});
```

### Pros:
- Very secure for known server environments
- Good for B2B partnerships with fixed infrastructure

### Cons:
- Not suitable for dynamic hosting environments
- Difficult to manage for multiple clients
- Issues with CDNs, load balancers, and proxy servers

## 6. Authentication-based Access

Require authentication before serving the script.

### Implementation:
```javascript
// Authentication middleware
function authenticateWidget(req, res, next) {
    const token = req.headers.authorization || req.query.token;
    
    if (validateToken(token)) {
        next();
    } else {
        res.status(401).send('Authentication required');
    }
}

app.get('/agent-chat-embed.js', authenticateWidget, (req, res) => {
    res.sendFile(path.join(__dirname, 'agent-chat-embed.js'));
});
```

### Usage:
```html
<script src="https://your-domain.com/agent-chat-embed.js?token=your_auth_token"></script>
```

### Pros:
- Highest security level
- Can integrate with existing auth systems
- Full control over access

### Cons:
- Most complex to implement
- Requires token management
- May complicate embedding process

## 7. Time-based Tokens

Generate time-limited tokens for script access.

### Implementation:
```javascript
const jwt = require('jsonwebtoken');

// Generate token (on your admin panel)
function generateEmbedToken(domain, expiresIn = '30d') {
    return jwt.sign(
        { 
            domain: domain,
            purpose: 'chat-embed'
        },
        process.env.JWT_SECRET,
        { expiresIn }
    );
}

// Validate token (in your server route)
app.get('/agent-chat-embed.js', (req, res) => {
    const token = req.query.token;
    
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const requestDomain = req.get('Referer');
        
        if (decoded.purpose === 'chat-embed' && requestDomain.includes(decoded.domain)) {
            res.sendFile(path.join(__dirname, 'agent-chat-embed.js'));
        } else {
            res.status(403).send('Invalid token or domain mismatch');
        }
    } catch (error) {
        res.status(401).send('Invalid or expired token');
    }
});
```

### Pros:
- Automatic expiration
- Can encode additional metadata
- Cryptographically secure

### Cons:
- Requires JWT library
- Token regeneration needed
- Clock synchronization important

## Recommended Implementation Strategy

### For Most Use Cases:
Combine **Referrer-based Restrictions (#1)** + **Domain Validation (#2)**

```javascript
// Server-side
app.get('/agent-chat-embed.js', (req, res) => {
    const allowedDomains = ['example.com', 'partner-site.com'];
    const referrer = req.get('Referer');
    
    if (referrer && allowedDomains.some(domain => referrer.includes(domain))) {
        res.sendFile(path.join(__dirname, 'agent-chat-embed.js'));
    } else {
        res.status(403).send('Access denied');
    }
});

// In agent-chat-embed.js
(function() {
    const allowedDomains = ['example.com', 'partner-site.com'];
    const currentDomain = window.location.hostname;
    
    if (!allowedDomains.some(domain => currentDomain.includes(domain))) {
        return;
    }
    
    // ... rest of embed code
})();
```

### For High Security Requirements:
Combine **API Key System (#3)** + **Server-side Validation** + **CORS (#4)**

### For B2B/Enterprise:
**IP Whitelisting (#5)** + **Authentication (#6)**

## Implementation Notes

- Always validate on both client and server side
- Log unauthorized access attempts for monitoring
- Consider rate limiting to prevent abuse
- Use HTTPS for all embed script requests
- Regularly rotate API keys/tokens
- Monitor usage patterns for anomalies

## Testing Your Implementation

1. Test from allowed domains - should work
2. Test from unauthorized domains - should fail
3. Test with modified scripts - should fail
4. Test with expired tokens - should fail
5. Monitor server logs for blocked attempts