#!/bin/bash

# FrontDesk Landing Page Build Script
# This script builds an optimized static website with 11ty in the dist folder

echo "🚀 Starting build process for FrontDesk Landing Page using Eleventy (11ty)..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js to continue."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm to continue."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Clean the dist directory
echo "🧹 Cleaning previous build..."
npm run clean

# Run the build script
echo "🔨 Building optimized static website with 11ty..."
npm run build

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build completed successfully!"
    echo "📁 The optimized static website is available in the 'dist' folder."
    echo "💡 You can deploy this folder to any static hosting service."
    
    # Print the size of the dist folder
    SIZE=$(du -sh dist | cut -f1)
    echo "📊 Total size of the dist folder: $SIZE"
    
    # Count files in the dist folder
    FILE_COUNT=$(find dist -type f | wc -l | xargs)
    echo "📄 Total number of files: $FILE_COUNT"
    
    # Show HTML file sizes
    echo "📄 HTML file sizes:"
    find dist -name "*.html" -exec du -h {} \; | sort -hr
    
    # Show CSS file sizes
    echo "🎨 CSS file sizes:"
    find dist -name "*.css" -exec du -h {} \; | sort -hr
    
    # Show JS file sizes
    echo "📜 JavaScript file sizes:"
    find dist -name "*.js" -exec du -h {} \; | sort -hr
    
    echo "⚡ Eleventy build complete! The site is ready for deployment."
else
    echo "❌ Build failed. Please check the error messages above."
    exit 1
fi 