/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'jit',
  content: [
    "./index.html",
    "./src/**/*.{js,html}"
  ],
  theme: {
    extend: {
      colors: {
        'black': '#000000',
        'white': '#FFFFFF',
        'green': '#55FF00',
        'blue': {
          'royal': '#0015FF',
          'light': 'rgba(255, 255, 255, 0.2)',
          'dark': '#0F1BA0'
        },
        'purple': '#5A359A',
        'transparent-white': 'rgba(255, 255, 255, 0.3)'
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
        'mulish': ['Mulish', 'sans-serif']
      },
      borderRadius: {
        'xl': '40px',
        '2xl': '50px'
      },
      backgroundImage: {
        'gradient-purple-blue': 'linear-gradient(180deg, #5A359A 0%, #0F1BA0 100%)'
      }
    }
  },
  plugins: []
} 