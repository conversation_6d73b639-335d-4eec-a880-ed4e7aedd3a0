document.addEventListener('DOMContentLoaded', function() {
  // Carousel functionality
  const dots = document.querySelectorAll('.pagination-dot');
  const slides = document.querySelectorAll('.carousel-item');
  
  // Add click event to each dot
  dots.forEach(dot => {
    dot.addEventListener('click', function() {
      const slideIndex = this.getAttribute('data-slide');
      showSlide(parseInt(slideIndex));
    });
  });
  
  // Auto-rotate slides every 7 seconds
  let currentSlide = 0;
  const slideCount = slides.length;
  
  function showSlide(index) {
    // Update current slide index
    currentSlide = index;
    
    // Calculate positions for all slides (previous, active, next)
    slides.forEach((slide, idx) => {
      // Remove all position classes first
      slide.classList.remove('active', 'prev-slide', 'next-slide');
      
      // Calculate relative position (0 = active, 1 = next, 2 = previous in 3-slide carousel)
      let relativePosition = (slideCount + idx - currentSlide) % slideCount;
      
      if (relativePosition === 0) {
        // Current slide - active position
        slide.classList.add('active');
        slide.style.transform = 'translateX(-50%) scale(1)';
        slide.style.zIndex = '5';
        slide.style.opacity = '1';
      } else if (relativePosition === 1) {
        // Next slide - right position
        slide.classList.add('next-slide');
        slide.style.transform = 'translate(calc(-50% - 20px), -20px)';
        slide.style.zIndex = '4';
        slide.style.opacity = '1';
      } else {
        // Previous slide - left position
        slide.classList.add('prev-slide');
        slide.style.transform = 'translate(calc(-50% - 40px), -40px)';
        slide.style.zIndex = '3';
        slide.style.opacity = '1';
      }
    });
    
    // Remove active class from all dots
    dots.forEach(dot => {
      dot.classList.remove('active');
    });
    
    // Activate the corresponding dot
    dots[index].classList.add('active');
  }
  
  function autoRotate() {
    currentSlide = (currentSlide + 1) % slideCount;
    showSlide(currentSlide);
  }
  
  // Start the auto-rotation
  let carouselInterval = setInterval(autoRotate, 7000);
  
  // Initialize the first slide
  showSlide(currentSlide);
  
  // Pause auto-rotation when hovering over the carousel
  const carousel = document.querySelector('.chat-carousel');
  if (carousel) {
    carousel.addEventListener('mouseenter', function() {
      clearInterval(carouselInterval);
    });
    
    carousel.addEventListener('mouseleave', function() {
      // Clear any existing interval before starting a new one
      clearInterval(carouselInterval);
      carouselInterval = setInterval(autoRotate, 7000);
    });
  }
}); 