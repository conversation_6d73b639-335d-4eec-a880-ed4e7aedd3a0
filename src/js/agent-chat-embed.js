(function() {
    'use strict';
    
    // Chat widget configuration
    const WIDGET_CONFIG = {
        src: 'https://frontdesk.doctor/hpo/hpo.html',
        width: '400px',
        height: '600px',
        position: 'top-right'
    };

    // Create the chat widget iframe
    function createChatWidget(config = {}) {
        // Merge user config with defaults
        const finalConfig = Object.assign({}, WIDGET_CONFIG, config);
        
        // Create iframe element
        const iframe = document.createElement('iframe');
        iframe.src = finalConfig.src;
        iframe.style.cssText = `
            position: fixed;
            z-index: 9999;
            border: none;
            border-radius: 0;
            width: ${finalConfig.width};
            height: ${finalConfig.height};
            ${getPositionStyles(finalConfig.position)}
            transition: all 0.3s ease;
        `;
        
        // Set iframe attributes for security and performance
        iframe.setAttribute('frameborder', '0');
        iframe.setAttribute('allowtransparency', 'true');
        iframe.setAttribute('scrolling', 'no');
        iframe.setAttribute('title', 'Hudson Pro Ortho Chat');
        
        return iframe;
    }
    
    // Get CSS positioning styles based on position preference
    function getPositionStyles(position) {
        const positions = {
            'bottom-right': 'bottom: 20px; right: 20px;',
            'bottom-left': 'bottom: 20px; left: 20px;',
            'top-right': 'top: 0; right: 0;',
            'top-left': 'top: 20px; left: 20px;'
        };
        return positions[position] || positions['bottom-right'];
    }
    
    // Initialize the chat widget
    function initializeChatWidget(config) {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                addChatWidget(config);
            });
        } else {
            addChatWidget(config);
        }
    }
    
    // Add chat widget to the page
    function addChatWidget(config) {
        // Check if widget already exists
        if (document.getElementById('hpo-chat-widget')) {
            return;
        }
        
        const iframe = createChatWidget(config);
        iframe.id = 'hpo-chat-widget';
        
        // Add to page
        document.body.appendChild(iframe);
        
        // Add responsive behavior
        addResponsiveBehavior(iframe);
    }
    
    // Add responsive behavior for mobile devices
    function addResponsiveBehavior(iframe) {
        function updateForMobile() {
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.top = '0';
                iframe.style.left = '0';
                iframe.style.right = 'auto';
                iframe.style.bottom = 'auto';
                iframe.style.borderRadius = '0';
            } else {
                iframe.style.width = WIDGET_CONFIG.width;
                iframe.style.height = WIDGET_CONFIG.height;
                iframe.style.cssText = iframe.style.cssText.replace(/top: 0px?;?/, '').replace(/left: 0px?;?/, '');
                iframe.style.cssText += getPositionStyles(WIDGET_CONFIG.position);
            }
        }
        
        // Initial check
        updateForMobile();
        
        // Listen for window resize
        window.addEventListener('resize', updateForMobile);
    }
    
    // Expose global function for manual initialization
    window.HPOChat = {
        init: initializeChatWidget,
        remove: function() {
            const widget = document.getElementById('hpo-chat-widget');
            if (widget) {
                widget.remove();
            }
        }
    };
    
    // Auto-initialize if script tag has data attributes
    const currentScript = document.currentScript || document.querySelector('script[src*="embed.js"]');
    if (currentScript) {
        const customConfig = {};
        
        // Check for data attributes
        if (currentScript.dataset.src) customConfig.src = currentScript.dataset.src;
        if (currentScript.dataset.width) customConfig.width = currentScript.dataset.width;
        if (currentScript.dataset.height) customConfig.height = currentScript.dataset.height;
        if (currentScript.dataset.position) customConfig.position = currentScript.dataset.position;
        
        // Auto-initialize
        initializeChatWidget(customConfig);
    }
})();