document.addEventListener('DOMContentLoaded', () => {
  // Mobile menu functionality
  const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
  const mobileMenuClose = document.getElementById('mobile-menu-close');
  const mobileMenu = document.getElementById('mobile-menu');
  
  // Open mobile menu
  if (mobileMenuToggle) {
    mobileMenuToggle.addEventListener('click', () => {
      mobileMenu.style.display = 'flex';
      document.body.style.overflow = 'hidden'; // Prevent scrolling when menu is open
    });
  }
  
  // Close mobile menu
  if (mobileMenuClose) {
    mobileMenuClose.addEventListener('click', () => {
      mobileMenu.style.display = 'none';
      document.body.style.overflow = ''; // Re-enable scrolling
    });
  }

  // Close menu when clicking on a mobile menu link
  const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');
  mobileNavLinks.forEach(link => {
    link.addEventListener('click', () => {
      mobileMenu.style.display = 'none';
      document.body.style.overflow = ''; // Re-enable scrolling
    });
  });

  // Add smooth scrolling to all links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      
      const targetId = this.getAttribute('href');
      if (targetId === '#') return;
      
      const targetElement = document.querySelector(targetId);
      if (targetElement) {
        // No offset to make section appear at the top of the screen
        const headerOffset = 0;
        const elementPosition = targetElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
        
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
        
        // If we're in mobile view and the menu is open, close it
        if (mobileMenu && window.getComputedStyle(mobileMenu).display !== 'none') {
          mobileMenu.style.display = 'none';
          document.body.style.overflow = ''; // Re-enable scrolling
        }
      }
    });
  });

  // Add hover effect to buttons
  document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('mouseenter', () => {
      button.style.transform = 'translateY(-2px)';
      button.style.transition = 'all 0.3s ease';
    });
    
    button.addEventListener('mouseleave', () => {
      button.style.transform = 'translateY(0)';
    });
  });
  
  // Animated text appearance
  const animateText = () => {
    const messageElement = document.getElementById('animated-message');
    const progressBar = document.getElementById('progress-bar');
    const text = "Hello! Thank you for calling our Clinic. How can I assist you today?";
    
    // Clear any existing content
    messageElement.innerHTML = '';
    
    // Create span for each character
    const characters = text.split('');
    characters.forEach(char => {
      const span = document.createElement('span');
      span.className = 'animated-text';
      
      // Preserve spaces by using non-breaking space for empty spaces
      span.innerHTML = char === ' ' ? '&nbsp;' : char;
      
      messageElement.appendChild(span);
    });
    
    // Get all spans
    const spans = messageElement.querySelectorAll('.animated-text');
    const totalChars = spans.length;
    
    // Animate each character one by one
    let currentChar = 0;
    
    const animationInterval = setInterval(() => {
      if (currentChar < totalChars) {
        // Animate the current character
        spans[currentChar].style.animationPlayState = 'running';
        
        // Update progress bar
        const progress = ((currentChar + 1) / totalChars) * 100;
        progressBar.style.width = `${progress}%`;
        
        currentChar++;
      } else {
        clearInterval(animationInterval);
      }
    }, 50); // Speed of animation (50ms per character)
  };
  
  // Start animation after a short delay
  setTimeout(animateText, 500);
  
  // Set current year in footer copyright
  const currentYearElement = document.getElementById('current-year');
  if (currentYearElement) {
    currentYearElement.textContent = new Date().getFullYear();
  }
}); 