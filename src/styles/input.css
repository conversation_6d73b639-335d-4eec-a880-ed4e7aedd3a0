@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Mulish:wght@400;600;700;1000&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply font-inter text-base;
  }
  
  h1 {
    @apply font-inter font-normal;
    letter-spacing: -0.01em;
  }
  
  h2, h3, h4 {
    @apply font-mulish;
  }
}

@layer components {
  .btn {
    @apply font-mulish font-bold text-base py-2 px-5 rounded-full flex items-center gap-3 transition-transform duration-300;
  }
  .btn-primary {
    @apply bg-white text-black;
  }
  .btn-outline {
    @apply border-2 border-white text-white;
  }
  
  .nav-link {
    @apply text-white font-inter text-base hover:opacity-80 transition-opacity;
  }
  
  .logo-text {
    @apply text-white font-mulish font-bold text-3xl;
  }
  
  .logo-ai {
    @apply text-white font-mulish font-black text-xl leading-none ml-1;
  }
  
  .feature-card {
    @apply flex items-start gap-3;
  }
  
  .feature-check {
    @apply text-green flex-shrink-0;
  }
  
  .section-title {
    @apply text-5xl font-mulish font-semibold text-center mb-16;
  }
  
  .section-subtitle {
    @apply text-3xl font-mulish font-semibold mb-4;
  }
}

@layer utilities {
  .backdrop-blur {
    backdrop-filter: blur(4px);
  }
  
  .text-shadow {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
  
  .shadow-lg {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }
  
  .hero-heading {
    line-height: 1.21;
  }
} 