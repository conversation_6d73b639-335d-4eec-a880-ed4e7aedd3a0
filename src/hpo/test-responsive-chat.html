<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HPO Chat Responsive Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .screen-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
        }
        
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .resize-buttons {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        
        .resize-btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .resize-btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="screen-info" id="screenInfo">
        Screen: <span id="screenSize"></span><br>
        Type: <span id="screenType"></span>
    </div>
    
    <div class="test-container">
        <h1>HPO Chat Responsive Test</h1>
        
        <div class="instructions">
            <h3>Test Instructions:</h3>
            <ul>
                <li><strong>Desktop (>1024px):</strong> Chat should be positioned top-right with full text</li>
                <li><strong>Tablet (481px-1024px):</strong> Chat should be positioned bottom-left, opening upward</li>
                <li><strong>Mobile (≤480px):</strong> Chat should be same position as desktop but 30% width, icon only (no text)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Screen Size Testing</h3>
            <p>Use these buttons to quickly test different screen sizes:</p>
            <div class="resize-buttons">
                <button class="resize-btn" onclick="resizeWindow(1200, 800)">Desktop (1200x800)</button>
                <button class="resize-btn" onclick="resizeWindow(768, 1024)">Tablet (768x1024)</button>
                <button class="resize-btn" onclick="resizeWindow(375, 667)">Mobile (375x667)</button>
            </div>
            <p><em>Note: Window resizing may not work in all browsers due to security restrictions. Try manually resizing the browser window instead.</em></p>
        </div>
        
        <div class="test-section">
            <h3>Expected Behavior:</h3>
            <ul>
                <li>Chat iframe size should remain consistent across all screen sizes</li>
                <li>On tablet, chat should open upward from bottom-left corner</li>
                <li>On mobile, chat button should be 30% width with no text, just icon</li>
                <li>Chat functionality should work the same on all devices</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Content for Testing</h3>
            <p>This is sample content to fill the page and test how the chat widget positions itself relative to the page content.</p>
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
            <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        </div>
    </div>

    <!-- Load the HPO Chat Widget -->
    <script src="./agent-chat-embed.js"></script>
    
    <script>
        function updateScreenInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            let screenType;
            if (width <= 480) {
                screenType = 'Mobile';
            } else if (width <= 1024) {
                screenType = 'Tablet';
            } else {
                screenType = 'Desktop';
            }
            
            document.getElementById('screenSize').textContent = `${width}x${height}`;
            document.getElementById('screenType').textContent = screenType;
        }
        
        function resizeWindow(width, height) {
            try {
                window.resizeTo(width, height);
            } catch (e) {
                alert('Window resizing is not allowed in this browser. Please manually resize the browser window to test different screen sizes.');
            }
        }
        
        // Update screen info on load and resize
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // Initialize the chat widget
        if (window.HPOChat) {
            window.HPOChat.init();
        }
    </script>
</body>
</html>
