<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" href="https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/themes/df-messenger-default.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Syncopate:wght@400;700&family=DM+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/df-messenger.js"></script>
<style>
        /* Enhanced Dialogflow Messenger Styling */
        /* https://cloud.google.com/dialogflow/cx/docs/concept/integration/dialogflow-messenger/css */
        df-messenger {
            z-index: 999;
            position: fixed;
            top: 0;
            right: 0;
            bottom: auto;
            left: auto;
            margin: 0 !important;
            --df-messenger-titlebar-title-font-family: 'Syncopate', sans-serif;
            --df-messenger-titlebar-title-font-size: 16px;
            --df-messenger-font-family: 'DM Sans', sans-serif;
            --df-messenger-font-color: #0e2c50;
            --df-messenger-font-size: 14px;
            /* --df-messenger-chat-border: 1px solid #0e2c50; */
            --df-messenger-chat-background: #ffffff;
            --df-messenger-message-user-background: #1d4376;
            --df-messenger-message-bot-background: #f8f9fa;
            --df-messenger-message-bot-font-color: #0e2c50;
            --df-messenger-message-user-font-color: #ffffff;
            --df-messenger-chat-border-radius: 0;
            --df-messenger-default-chat-border-radius: 0;
            --df-messenger-chat-bubble-background: #A976FF;
            --df-messenger-chat-bubble-font-color: #ffffff;
            --df-messenger-chat-bubble-border-radius: 0;
            --df-messenger-chat-border-radius-minimized: 0;
            --df-messenger-input-background: #f8f9fa;
            --df-messenger-input-font-color: #0e2c50;
            --df-messenger-input-border-color: #0091ce;
            --df-messenger-input-placeholder-font-color: #1d4376;
            --df-messenger-titlebar-background: #1d4376;
            --df-messenger-titlebar-font-color: #ffffff;
            --df-messenger-titlebar-title-font-weight: 600;
            --df-messenger-titlebar-icon-font-color: #ffffff;
            --df-messenger-chat-overflow: auto;
            --df-messenger-chat-bubble-icon-color: #ffffff;
        }
    </style>
  </head>
  <body>
    <div id="root"></div>

<df-messenger
  project-id="frontdesk-454309"
  agent-id="7773dff3-6832-4fd6-9011-a49280fb4436"
  language-code="en"
  max-query-length="-1">
  <df-messenger-chat-bubble
    chat-title="Chat with Isabella">
  </df-messenger-chat-bubble>
</df-messenger>

  <script>
    // Comprehensive shadow DOM style override for nested Dialogflow components
    document.addEventListener('DOMContentLoaded', function() {
      const buttonText = 'Chat with Agent';
      function generateResponsiveCSS() {
        const isTablet = window.parent.innerWidth > 480 && window.parent.innerWidth <= 1024;
        const isMobile = window.parent.innerWidth <= 480;

        return `
            .chat-wrapper {
              ${isTablet ? `
                bottom: 0 !important;
                left: 0 !important;
                top: auto !important;
                right: auto !important;
                transform-origin: bottom left !important;
              ` : `
                top: 0 !important;
                right: 0 !important;
                bottom: auto !important;
                left: auto !important;
                transform-origin: top right !important;
              `}
            }
            .message-list-wrapper {
              border-radius: 0 !important;
            }
            .chat-bubble-button {
              border-radius: 0 !important;
            }
            .close-button {
              ${isMobile ? `
                padding: 6px !important;
                min-width: auto !important;
                width: auto !important;
              ` : `
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                gap: 2px !important;
                padding: 6px 12px !important;
                width: auto !important;
                height: auto !important;
                position: relative !important;
              `}
            }
            .close-button.bubble {
              ${isMobile ? `
                padding: 6px !important;
                gap: 0 !important;
              ` : `
                gap: 2px !important;
              `}
            }
            .close-button:not(.bubble) {
              gap: 0 !important;
              justify-content: flex-start !important;
              padding: 12px !important;
            }
            .close-button:not(.bubble) .close-icon {
              position: absolute !important;
              left: 12px !important;
              top: 50% !important;
              transform: translateY(-50%) !important;
            }
            .close-button:not(.bubble)::after {
              opacity: 0 !important;
              visibility: hidden !important;
            }
            .close-button .icon {
              ${isMobile ? `
                justify-content: center;
              ` : `
                display: flex !important;
                align-items: center !important;
              `}
            }
            .close-button .icon svg {
              width: 24px !important;
              height: 24px !important;
            }

            .close-button::after {
            ${isMobile ? `
                display: none !important;
                content: "" !important;
              ` : `
                display: block !important;
                content: "${buttonText}";
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Syncopate', sans-serif;
                white-space: nowrap;
              `}
            }
            .close-icon {
              display: flex !important;
              align-items: center !important;
            }
            .close-icon svg {
              width: 24px !important;
              height: 24px !important;
            }
          `;
      }

      function injectStylesIntoShadowRoot(shadowRoot, styleId, styles) {
        if (shadowRoot && !shadowRoot.querySelector(`#${styleId}`)) {
          const style = document.createElement('style');
          style.id = styleId;
          style.textContent = styles;
          shadowRoot.appendChild(style);
          return true;
        }
        return false;
      }

      function findAndStyleAllShadowRoots() {
        // Generate current responsive CSS
        const currentCssStyle = generateResponsiveCSS();

        // Find all possible Dialogflow components
        const components = [
          'df-messenger',
          'df-messenger-chat-bubble',
          'df-messenger-chat',
          'df-messenger-message-list',
          'df-messenger-user-input',
          'df-messenger-titlebar'
        ];

        components.forEach(componentName => {
          const elements = document.querySelectorAll(componentName);
          elements.forEach((element, index) => {
            if (element.shadowRoot) {
              // Inject styles for positioning and custom text
              injectStylesIntoShadowRoot(element.shadowRoot, `${componentName}-styles-${index}`, currentCssStyle);

              // Recursively search for nested shadow roots
              searchNestedShadowRoots(element.shadowRoot, `${componentName}-${index}`, currentCssStyle);
            }
          });
        });
      }

      function searchNestedShadowRoots(shadowRoot, parentName, cssToInject) {
        const allElements = shadowRoot.querySelectorAll('*');
        allElements.forEach((element, index) => {
          if (element.shadowRoot) {
            const nestedId = `${parentName}-nested-${index}`;
            injectStylesIntoShadowRoot(element.shadowRoot, nestedId, cssToInject);

            // Continue searching deeper
            searchNestedShadowRoots(element.shadowRoot, nestedId, cssToInject);
          }
        });
      }

      // Run multiple times with increasing delays
      const delays = [500, 1000, 2000, 3000, 5000];
      delays.forEach(delay => {
        setTimeout(findAndStyleAllShadowRoots, delay);
      });

      // Listen for Dialogflow events
      document.addEventListener('df-messenger-loaded', findAndStyleAllShadowRoots);
      document.addEventListener('df-chat-opened', findAndStyleAllShadowRoots);

      // Use MutationObserver to watch for new shadow roots
      const observer = new MutationObserver((mutations) => {
        let shouldCheck = false;
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // Check if it's a Dialogflow component or has shadow root
                if (node.tagName && node.tagName.startsWith('DF-') || node.shadowRoot) {
                  shouldCheck = true;
                }
              }
            });
          }
        });

        if (shouldCheck) {
          setTimeout(findAndStyleAllShadowRoots, 100);
        }
      });

      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      // Listen for window resize to update responsive styles
      window.addEventListener('resize', () => {
        // Regenerate CSS with updated screen size detection
        const newCssStyle = generateResponsiveCSS();

        // Update all shadow roots with new styles
        const components = [
          'df-messenger',
          'df-messenger-chat-bubble',
          'df-messenger-chat',
          'df-messenger-message-list',
          'df-messenger-user-input',
          'df-messenger-titlebar'
        ];

        components.forEach(componentName => {
          const elements = document.querySelectorAll(componentName);
          elements.forEach((element, index) => {
            if (element.shadowRoot) {
              // Remove old styles and inject new ones
              const oldStyle = element.shadowRoot.querySelector(`#${componentName}-styles-${index}`);
              if (oldStyle) oldStyle.remove();

              injectStylesIntoShadowRoot(element.shadowRoot, `${componentName}-styles-${index}`, newCssStyle);

              // Update nested shadow roots
              const allElements = element.shadowRoot.querySelectorAll('*');
              allElements.forEach((nestedElement, nestedIndex) => {
                if (nestedElement.shadowRoot) {
                  const nestedId = `${componentName}-${index}-nested-${nestedIndex}`;
                  const oldNestedStyle = nestedElement.shadowRoot.querySelector(`#${nestedId}`);
                  if (oldNestedStyle) oldNestedStyle.remove();

                  injectStylesIntoShadowRoot(nestedElement.shadowRoot, nestedId, newCssStyle);
                }
              });
            }
          });
        });
      });
    });
  </script>
  <script src="./hpo-iframe.js"></script>
  </body>
</html>
