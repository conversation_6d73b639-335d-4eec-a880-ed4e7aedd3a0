(function () {
  ("use strict");

  // Auto-send a greeting when chat opens (once per session)
  let __hpoHiSent = false;
  
  // Track if chat has been intentionally opened to prevent auto-close
  let __hpoChatIntentionallyOpened = false;

  function trySendHiViaUserInput() {
    try {
      const messenger = document.querySelector("df-messenger");
      if (!messenger) return false;

      // Deep query across nested shadow roots
      function deepQuery(start, selectors) {
        let selList = Array.isArray(selectors) ? selectors : [selectors];
        let queue = [];
        queue.push(start);
        if (start && start.shadowRoot) queue.push(start.shadowRoot);
        while (queue.length) {
          let node = queue.shift();
          if (!node) continue;

          if (node instanceof ShadowRoot || node instanceof DocumentFragment) {
            // Try selectors within this shadow root
            for (let s = 0; s < selList.length; s++) {
              let foundInSR = node.querySelector(selList[s]);
              if (foundInSR) return foundInSR;
            }
            let kids = node.children || [];
            for (let k = 0; k < kids.length; k++) queue.push(kids[k]);
            continue;
          }

          if (node.nodeType === Node.ELEMENT_NODE) {
            let el = node;
            // Check this element itself
            for (let s2 = 0; s2 < selList.length; s2++) {
              try {
                if (el.matches && el.matches(selList[s2])) return el;
              } catch (_) {}
            }
            // Check inside its shadow root
            if (el.shadowRoot) {
              for (let s3 = 0; s3 < selList.length; s3++) {
                const foundDeep = el.shadowRoot.querySelector(selList[s3]);
                if (foundDeep) return foundDeep;
              }
              queue.push(el.shadowRoot);
            }
            // Enqueue light DOM children
            for (let j = 0; j < el.children.length; j++)
              queue.push(el.children[j]);
          }
        }
        return null;
      }

      function findInput(start) {
        let queue = [];
        queue.push(start);
        if (start && start.shadowRoot) queue.push(start.shadowRoot);
        while (queue.length) {
          let node = queue.shift();
          if (!node) continue;

          if (node instanceof ShadowRoot || node instanceof DocumentFragment) {
            let children = node.children || [];
            for (let i = 0; i < children.length; i++) queue.push(children[i]);
            continue;
          }

          if (node.nodeType === Node.ELEMENT_NODE) {
            let el = node;
            if (el.shadowRoot) {
              const ta = el.shadowRoot.querySelector(
                "df-messenger-user-input [contenteditable], df-messenger-user-input textarea, df-messenger-user-input input, [contenteditable], textarea, input"
              );
              if (ta) return { input: ta, root: el.shadowRoot };
              queue.push(el.shadowRoot);
            }
            for (let j = 0; j < el.children.length; j++)
              queue.push(el.children[j]);
          }
        }
        return null;
      }

      const found = findInput(messenger);
      if (!found || !found.input) return false;

      const input = found.input;
      if (typeof input.focus === "function") input.focus();
      const isEditable =
        (input.getAttribute &&
          (input.getAttribute("contenteditable") === "" ||
            input.getAttribute("contenteditable") === "true")) ||
        input.isContentEditable === true;
      if (isEditable) {
        input.textContent = "hi";
      } else {
        input.value = "hi";
      }
      input.dispatchEvent(
        new Event("input", { bubbles: true, composed: true })
      );

      // Try to find the send button robustly across shadow roots
      const sendButton =
        deepQuery(found.root || messenger, [
          "#send-icon-button",
          '[aria-label="Send"]',
          '[aria-label*="send" i]',
          ".send-button",
          ".send-icon",
          'button[type="submit"]',
        ]) || null;
      if (sendButton) {
        try {
          // Synthesize a full user-like click sequence
          const rect = (sendButton.getBoundingClientRect &&
            sendButton.getBoundingClientRect()) || { left: 0, top: 0 };
          const coords = { clientX: rect.left + 1, clientY: rect.top + 1 };
          const pointerDown = new PointerEvent(
            "pointerdown",
            Object.assign(
              {
                bubbles: true,
                composed: true,
                cancelable: true,
                pointerId: 1,
                isPrimary: true,
                pointerType: "mouse",
              },
              coords
            )
          );
          const mouseDown = new MouseEvent(
            "mousedown",
            Object.assign(
              { bubbles: true, composed: true, cancelable: true, button: 0 },
              coords
            )
          );
          const pointerUp = new PointerEvent(
            "pointerup",
            Object.assign(
              {
                bubbles: true,
                composed: true,
                cancelable: true,
                pointerId: 1,
                isPrimary: true,
                pointerType: "mouse",
              },
              coords
            )
          );
          const mouseUp = new MouseEvent(
            "mouseup",
            Object.assign(
              { bubbles: true, composed: true, cancelable: true, button: 0 },
              coords
            )
          );
          const clickEv = new MouseEvent(
            "click",
            Object.assign(
              { bubbles: true, composed: true, cancelable: true, button: 0 },
              coords
            )
          );
          sendButton.dispatchEvent(pointerDown);
          sendButton.dispatchEvent(mouseDown);
          sendButton.dispatchEvent(pointerUp);
          sendButton.dispatchEvent(mouseUp);
          sendButton.dispatchEvent(clickEv);
        } catch (_) {
          try {
            sendButton.click && sendButton.click();
          } catch (_) {}
        }
        return true;
      }

      // Try submitting the nearest form ancestor
      function findForm(el) {
        try {
          if (!el) return null;
          if (typeof el.closest === "function") {
            const form = el.closest("form");
            if (form) return form;
          }
          // Check within shadow root host trees
          const root = el.getRootNode && el.getRootNode();
          if (root && root.host) {
            return findForm(root.host);
          }
        } catch (_) {
          // ignore
        }
        return null;
      }
      const formEl = findForm(input);
      if (formEl) {
        try {
          if (typeof formEl.requestSubmit === "function") {
            formEl.requestSubmit();
          } else if (typeof formEl.submit === "function") {
            formEl.submit();
          } else {
            formEl.dispatchEvent(
              new Event("submit", {
                bubbles: true,
                composed: true,
                cancelable: true,
              })
            );
          }
        } catch (_) {
          try {
            formEl.dispatchEvent(
              new Event("submit", {
                bubbles: true,
                composed: true,
                cancelable: true,
              })
            );
          } catch (_) {}
        }
        return true;
      }

      // Fallback to Enter key events
      const eventInit = {
        key: "Enter",
        code: "Enter",
        keyCode: 13,
        which: 13,
        bubbles: true,
        composed: true,
      };
      const enterDown = new KeyboardEvent("keydown", eventInit);
      const enterPress = new KeyboardEvent("keypress", eventInit);
      const enterUp = new KeyboardEvent("keyup", eventInit);
      input.dispatchEvent(enterDown);
      input.dispatchEvent(enterPress);
      input.dispatchEvent(enterUp);
      // Try change event as a last resort
      input.dispatchEvent(
        new Event("change", { bubbles: true, composed: true })
      );
      return true;
    } catch (_) {
      return false;
    }
  }

  function sendHiWithRetries() {
    if (__hpoHiSent) return;
    const attempts = [0, 150, 350, 700, 1200, 2000, 3000];
    attempts.forEach(function (delay) {
      setTimeout(function () {
        if (__hpoHiSent) return;
        let sent = false;
        try {
          sent = trySendHiViaUserInput();
        } catch (_) {}
        if (sent) __hpoHiSent = true;
      }, delay);
    });
  }

  function handleChatOpenedEvent(e) {
    try {
      // df-chat-open-changed supplies detail.isOpen
      if (e && e.type === "df-chat-open-changed") {
        if (e.detail && e.detail.isOpen) {
          // Approach 1: try DOM-based send
          sendHiWithRetries();

          // Approach 2: one-time intercept of the next request and mutate to 'hi'
          if (!__hpoHiSent) {
            const once = function (evt) {
              try {
                if (__hpoHiSent) return;
                const messenger = document.querySelector("df-messenger");
                const lang =
                  (messenger &&
                    messenger.getAttribute &&
                    messenger.getAttribute("language-code")) ||
                  "en";
                const body =
                  evt &&
                  evt.detail &&
                  evt.detail.data &&
                  evt.detail.data.requestBody;
                if (!body) return;

                // Replace the outgoing request with our text query (let Messenger send it)
                evt.detail.data.requestBody = {
                  queryInput: {
                    text: { text: "hi" },
                    languageCode: lang,
                  },
                };
                __hpoHiSent = true;
                window.removeEventListener("df-request-sent", once, true);
              } catch (_) {
                // ignore
              }
            };
            // Use capture to ensure we intercept before send
            window.addEventListener("df-request-sent", once, true);

            // Proactively trigger a request so the interceptor can modify it
            try {
              const messengerEl = document.querySelector("df-messenger");
              if (
                messengerEl &&
                typeof messengerEl.startNewSession === "function"
              ) {
                messengerEl.startNewSession({ retainHistory: true });
              }
            } catch (_) {
              // ignore
            }
          }
        }
        return;
      }
      // only documented event is used
    } catch (_) {
      // ignore
    }
  }

  // For simple two-state behavior, only send open/close signals
  function notifyOpen() {
    window.parent.postMessage("expand-chat", "*");
  }

  function notifyClose() {
    window.parent.postMessage("minimize-chat", "*");
  }

  // Relay Dialogflow open state via the documented event
  window.addEventListener("df-chat-open-changed", function (event) {
    try {
      const isOpen =
        event && event.detail && typeof event.detail.isOpen === "boolean"
          ? event.detail.isOpen
          : undefined;
      if (isOpen === true) notifyOpen();
      else if (isOpen === false) notifyClose();
    } catch (_) {
      // ignore
    }
  });

  // Listen for chat open to auto-send greeting
  window.addEventListener("df-chat-open-changed", handleChatOpenedEvent);


  // Force close chat using official API on page load (only if not intentionally opened)
  function forceCloseChat() {
    // Don't auto-close if chat was intentionally opened
    if (__hpoChatIntentionallyOpened) return;
    
    try {
      const dfMessengerBubble = document.querySelector('df-messenger-chat-bubble');
      if (dfMessengerBubble && dfMessengerBubble.closeChat) {
        dfMessengerBubble.closeChat();
        notifyClose();
      }
    } catch (e) {
      // ignore
    }
  }

  // Run immediately and with delays to ensure it works (only on initial load)
  forceCloseChat();
  setTimeout(forceCloseChat, 500);
  setTimeout(forceCloseChat, 1500);
  
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", function() {
      forceCloseChat();
      setTimeout(forceCloseChat, 500);
    });
  }

  // Fallback: detect close via event composedPath, but do not auto-open on any click
  function handleInteract(e) {
    try {
      const path = typeof e.composedPath === "function" ? e.composedPath() : [];
      const candidates = [e.target].concat(path || []);
      for (let i = 0; i < candidates.length; i++) {
        const node = candidates[i];
        if (!node || node.nodeType !== Node.ELEMENT_NODE) continue;
        const el = node;
        const cls = (el.className && String(el.className)) || "";
        const aria = (el.getAttribute && el.getAttribute("aria-label")) || "";
        const title = (el.getAttribute && el.getAttribute("title")) || "";

        // If it's a "close-button" inside df-messenger-chat-bubble → check if chat is open
        if (/close-button/.test(cls)) {
          const root = (el.getRootNode && el.getRootNode()) || null;
          const host = root && root.host ? root.host : null;
          const hostTag =
            host && host.tagName ? String(host.tagName).toUpperCase() : "";
          if (hostTag === "DF-MESSENGER-CHAT-BUBBLE") {
            // Use proper API to open chat and mark as intentionally opened
            const dfMessengerBubble = document.querySelector('df-messenger-chat-bubble');
            if (dfMessengerBubble) {
              try {
                if (dfMessengerBubble.openChat) {
                  __hpoChatIntentionallyOpened = true;
                  dfMessengerBubble.openChat();
                  notifyOpen();
                }
              } catch (e) {
                __hpoChatIntentionallyOpened = true;
                notifyOpen(); // Fallback to open
              }
            } else {
              __hpoChatIntentionallyOpened = true;
              notifyOpen();
            }
            return;
          }
          // Otherwise, treat as a genuine close button inside chat → CLOSE (collapse)
          notifyClose();
          return;
        }

        // Generic close fallback by label/title
        if (/close/i.test(aria) || /close/i.test(title)) {
          notifyClose();
          return;
        }
      }
    } catch (_) {
      // ignore
    }
  }

  // Listen for interactions but don't interfere with normal click behavior
  document.addEventListener("click", handleInteract, false);

  // Keep behavior event-driven (bubble open/close), avoid generic focus/blur heuristics

  // Attach listeners inside Dialogflow shadow DOM to detect bubble open and close button clicks
  function wireShadowDomListeners() {
    try {
      const componentSelectors = [
        "df-messenger",
        "df-messenger-chat-bubble",
        "df-messenger-chat",
        "df-messenger-titlebar",
      ];

      componentSelectors.forEach(function (sel) {
        const nodes = document.querySelectorAll(sel);
        nodes.forEach(function (node) {
          if (!node || !node.shadowRoot) return;

          // In chat bubble, .close-button toggles open → treat as OPEN
          if (
            String(node.tagName).toUpperCase() === "DF-MESSENGER-CHAT-BUBBLE"
          ) {
            const bubbleToggles =
              node.shadowRoot.querySelectorAll(".close-button");
            bubbleToggles.forEach(function (btn) {
              if (!btn.__hpoOpenWired) {
                btn.addEventListener(
                  "click",
                  function () {
                    notifyOpen();
                  },
                  true
                );
                btn.__hpoOpenWired = true;
              }
            });
          } else {
            // In other components, .close-button means CLOSE
            const closeButtons =
              node.shadowRoot.querySelectorAll(".close-button");
            closeButtons.forEach(function (btn) {
              if (!btn.__hpoCloseWired) {
                btn.addEventListener(
                  "click",
                  function () {
                    notifyClose();
                  },
                  true
                );
                btn.__hpoCloseWired = true;
              }
            });
          }

          // Observe for dynamic DOM changes within this shadow root and rewire
          if (!node.__hpoObserver) {
            const observer = new MutationObserver(function () {
              setTimeout(wireShadowDomListeners, 0);
            });
            observer.observe(node.shadowRoot, {
              childList: true,
              subtree: true,
            });
            node.__hpoObserver = observer;
          }
        });
      });
    } catch (_) {
      // ignore
    }
  }

  // Try wiring listeners at multiple times
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", function () {
      wireShadowDomListeners();
      setTimeout(wireShadowDomListeners, 500);
      setTimeout(wireShadowDomListeners, 1500);
    });
  } else {
    wireShadowDomListeners();
    setTimeout(wireShadowDomListeners, 500);
    setTimeout(wireShadowDomListeners, 1500);
  }
})();


