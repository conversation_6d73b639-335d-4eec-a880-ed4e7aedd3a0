(function() {
  ("use strict");

  // Chat widget configuration
  const WIDGET_CONFIG = {
    src: "https://frontdesk.doctor/hpo/hpo.html",
    width: "320px", // expanded default
    height: "560px", // expanded default
    position: "top-right",
  };

  // Create the chat widget iframe
  function createChatWidget(config = {}) {
    // Merge user config with defaults
    const finalConfig = Object.assign({}, WIDGET_CONFIG, config);
    const collapsedWidth = "213px";
    const collapsedHeight = "48px";

    // Create iframe element
    const iframe = document.createElement("iframe");
    iframe.src = finalConfig.src;
    // Start in collapsed state by default; we will expand on message
    const isMobile = window.innerWidth <= 768;
    const initialWidth = collapsedWidth;
    const initialHeight = collapsedHeight;
    // Persist collapsed sizes on the element for later use
    iframe.dataset.collapsedWidth = collapsedWidth;
    iframe.dataset.collapsedHeight = collapsedHeight;
    iframe.style.cssText = `
            position: fixed;
            z-index: 9999;
            border: none;
            border-radius: 0;
            width: ${initialWidth};
            height: ${initialHeight};
            ${getPositionStyles(finalConfig.position)}
            transition: all 0.3s ease;
        `;

    // Set iframe attributes for security and performance
    iframe.setAttribute("frameborder", "0");
    iframe.setAttribute("allowtransparency", "true");
    iframe.setAttribute("scrolling", "no");
    iframe.setAttribute("title", "Hudson Pro Ortho Chat");

    return iframe;
  }

  // Get CSS positioning styles based on position preference
  function getPositionStyles(position) {
    const positions = {
      "bottom-right": "bottom: 20px; right: 20px;",
      "bottom-left": "bottom: 0; left: 0;",
      "top-right": "top: 0; right: 0;",
      "top-left": "top: 20px; left: 20px;",
    };
    return positions[position] || positions["bottom-right"];
  }

  // Initialize the chat widget
  function initializeChatWidget(config) {
    // Wait for DOM to be ready
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", function () {
        addChatWidget(config);
      });
    } else {
      addChatWidget(config);
    }
  }

  // Add chat widget to the page
  function addChatWidget(config) {
    // Check if widget already exists
    if (document.getElementById("hpo-chat-widget")) {
      return;
    }

    const iframe = createChatWidget(config);
    iframe.id = "hpo-chat-widget";

    // Add to page
    document.body.appendChild(iframe);

    // Add responsive behavior and message-based resizing
    addResponsiveBehavior(iframe);
    addMessageResizeBehavior(iframe, config);
  }

  // Add responsive behavior for mobile and tablet devices
  function addResponsiveBehavior(iframe) {
    // Track expansion state across resizes
    let isExpanded = false;

    function setExpandedState(nextExpanded, finalConfig = WIDGET_CONFIG) {
      isExpanded = !!nextExpanded;

      // Define breakpoints: mobile (≤480px), tablet (481px-1024px), desktop (>1024px)
      const isMobile = window.parent.innerWidth <= 480;
      const isTablet = window.parent.innerWidth > 480 && window.parent.innerWidth <= 1024;
      const isDesktop = window.parent.innerWidth > 1024;

      const collapsedWidth = iframe.dataset.collapsedWidth || "276px";
      const collapsedHeight = iframe.dataset.collapsedHeight || "100px";

      // Determine position based on screen size
      let position = finalConfig.position || WIDGET_CONFIG.position;
      if (isTablet) {
        position = "bottom-left";
      }

      if (isMobile) {
        // Mobile: same positioning as desktop but 30% width for collapsed state
        if (isExpanded) {
          iframe.style.width = finalConfig.width || WIDGET_CONFIG.width;
          iframe.style.height = finalConfig.height || WIDGET_CONFIG.height;
        } else {
          // 30% of collapsed width for mobile
          const mobileWidth = Math.round(parseInt(collapsedWidth) * 0.3) + "px";
          iframe.style.width = mobileWidth;
          iframe.style.height = collapsedHeight;
        }
        iframe.style.cssText = iframe.style.cssText
          .replace(/top: 0px?;?/, "")
          .replace(/left: 0px?;?/, "");
        iframe.style.cssText += getPositionStyles(position);
      } else {
        // Tablet and Desktop: normal behavior with appropriate positioning
        if (isExpanded) {
          iframe.style.width = finalConfig.width || WIDGET_CONFIG.width;
          iframe.style.height = finalConfig.height || WIDGET_CONFIG.height;
        } else {
          iframe.style.width = collapsedWidth;
          iframe.style.height = collapsedHeight;
        }
        iframe.style.cssText = iframe.style.cssText
          .replace(/top: 0px?;?/, "")
          .replace(/left: 0px?;?/, "");
        iframe.style.cssText += getPositionStyles(position);
      }
    }

    function updateForResponsive() {
      setExpandedState(isExpanded);
    }

    // Expose a way for message handler to update state
    iframe.__setExpandedState = setExpandedState;
    iframe.__getExpandedState = function () {
      return isExpanded;
    };

    // Initial state collapsed
    updateForResponsive();

    // Listen for window resize
    window.addEventListener("resize", updateForResponsive);
  }

  function addMessageResizeBehavior(iframe, config) {
    const finalConfig = Object.assign({}, WIDGET_CONFIG, config);
    function handleMessage(event) {
      try {
        // Only accept messages from our iframe
        if (event.source !== iframe.contentWindow) return;
        const data =
          typeof event.data === "string"
            ? event.data
            : event.data && (event.data.type || event.data.event);
        if (data === "expand-chat" || data === "df-chat-opened") {
          if (typeof iframe.__setExpandedState === "function") {
            iframe.__setExpandedState(true, finalConfig);
          }
        } else if (data === "minimize-chat" || data === "df-chat-closed") {
          if (typeof iframe.__setExpandedState === "function") {
            iframe.__setExpandedState(false, finalConfig);
          }
        }
      } catch (e) {
        // ignore
      }
    }
    window.addEventListener("message", handleMessage);
  }

  // No generic expand-on-interaction to avoid auto-expanding at load

  // Expose global function for manual initialization
  window.HPOChat = {
    init: initializeChatWidget,
    remove: function () {
      const widget = document.getElementById("hpo-chat-widget");
      if (widget) {
        widget.remove();
      }
    },
  };

  // Auto-initialize if script tag has data attributes
  const currentScript =
    document.currentScript || document.querySelector('script[src*="embed.js"]');
  if (currentScript) {
    const customConfig = {};

    // Check for data attributes
    if (currentScript.dataset.src) customConfig.src = currentScript.dataset.src;
    if (currentScript.dataset.width)
      customConfig.width = currentScript.dataset.width;
    if (currentScript.dataset.height)
      customConfig.height = currentScript.dataset.height;
    if (currentScript.dataset.position)
      customConfig.position = currentScript.dataset.position;

    // Auto-initialize
    initializeChatWidget(customConfig);
  }
})();