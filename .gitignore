# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
build/
out/
.next/
.nuxt/
.cache/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Coverage directories
coverage/
.nyc_output/

# IDE / Editor specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
*.sublime-project
*.sublime-workspace

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Yarn Integrity file
.yarn-integrity

# Yarn v2+
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# TypeScript cache
*.tsbuildinfo

# Local Netlify folder
.netlify

# Vercel
.vercel

# Serverless directories
.serverless/

# Webpack
.webpack/

# Parcel
.parcel-cache

# Temporary files
tmp/
temp/ 

# Screenshots
screenshots/
*.zip
