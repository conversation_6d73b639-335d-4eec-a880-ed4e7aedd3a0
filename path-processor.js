const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * A utility to fix paths in HTML files generated by 11ty
 * - Removes 'src/' prefix from asset paths
 */
function processFiles() {
  const htmlFiles = glob.sync('./dist/**/*.html');
  
  htmlFiles.forEach(filePath => {
    console.log(`Processing file: ${filePath}`);
    let content = fs.readFileSync(filePath, 'utf8');
    
    // Replace all 'src/' references
    content = content.replace(/src\//g, '');
    
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated file: ${filePath}`);
  });
}

module.exports = { processFiles }; 