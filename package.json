{"name": "frontdesk-landing", "version": "1.0.0", "description": "FrontDesk Landing Page", "main": "index.js", "scripts": {"dev": "npm-run-all --parallel dev:eleventy build:css", "dev:eleventy": "eleventy --serve", "build:css": "tailwindcss -i ./src/styles/input.css -o ./src/styles/output.css --watch", "build:css:prod": "tailwindcss -i ./src/styles/input.css -o ./src/styles/output.css --minify", "clean": "<PERSON><PERSON><PERSON> dist", "combine:css": "mkdir -p dist/styles && cat src/styles/output.css src/styles/custom.css > dist/styles/styles.css", "minify:html": "html-minifier-terser --collapse-whitespace --remove-comments --remove-optional-tags --remove-redundant-attributes --remove-script-type-attributes --remove-tag-whitespace --use-short-doctype --minify-css true --minify-js true -o dist/index.html dist/index.html", "minify:css": "cleancss -o dist/styles/styles.css dist/styles/styles.css", "minify:js": "for file in dist/js/*.js; do terser \"$file\" -o \"$file\" --compress --mangle; done", "build": "eleventy", "start": "npm run dev"}, "keywords": ["frontdesk", "landing", "page"], "author": "", "license": "ISC", "devDependencies": {"@11ty/eleventy": "^3.0.0", "clean-css-cli": "^5.6.3", "copyfiles": "^2.4.1", "glob": "^8.1.0", "html-minifier-terser": "^7.2.0", "live-server": "^1.2.2", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "tailwindcss": "^3.4.1", "terser": "^5.39.0"}}