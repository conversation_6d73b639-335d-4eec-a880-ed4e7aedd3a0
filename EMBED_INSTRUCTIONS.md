# Hudson Pro Ortho Chat Widget - Embedding Instructions

## Quick Start

To add the Hudson Pro Ortho chat widget to your website, choose one of the methods below:

### Method 1: Simple Script Tag (Recommended)

Add this single line to your HTML before the closing `</body>` tag:

```html
<script src="https://your-domain.com/agent-chat-embed.js"></script>
```

### Method 2: Manual iframe

Add this iframe directly to your HTML:

```html
<iframe 
    src="https://your-domain.com/hpo-iframe.html" 
    id="hpo-chat-widget"
    style="position: fixed; bottom: 20px; right: 20px; width: 400px; height: 600px; border: none; border-radius: 0; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); z-index: 9999;"
    frameborder="0" 
    allowtransparency="true" 
    scrolling="no" 
    title="Hudson Pro Ortho Chat">
</iframe>
```

## Advanced Configuration

### Customizing the embed script

You can customize the widget position, size, and source using data attributes:

```html
<script 
    src="https://your-domain.com/agent-chat-embed.js"
    data-position="bottom-left"
    data-width="350px"
    data-height="550px">
</script>
```

Available options:
- **data-position**: `bottom-right` (default), `bottom-left`, `top-right`, `top-left`
- **data-width**: Any valid CSS width (default: `400px`)
- **data-height**: Any valid CSS height (default: `600px`)
- **data-src**: Custom iframe source URL

### Using JavaScript API

For more control, use the JavaScript API:

```html
<script src="https://your-domain.com/agent-chat-embed.js"></script>
<script>
// Initialize with custom configuration
HPOChat.init({
    position: 'bottom-left',
    width: '350px',
    height: '550px'
});

// Remove the widget
HPOChat.remove();
</script>
```

## Responsive Behavior

The widget automatically adapts to mobile devices:
- On screens wider than 768px: Shows as positioned widget with specified dimensions
- On mobile (≤768px): Expands to full screen for better usability

## Files You Need to Host

To use these embedding options, you need to host these files on your domain:

1. **hpo-iframe.html** - The chat widget HTML file
2. **agent-chat-embed.js** - The embedding script (optional, for script tag method)

## Important Notes

- Replace `https://your-domain.com/` with your actual domain in all examples
- The widget uses external resources (Google Fonts, Dialogflow) - ensure these aren't blocked
- The chat connects to Dialogflow project `frontdesk-454309` with agent `7773dff3-6832-4fd6-9011-a49280fb4436`
- Widget has a high z-index (9999) to appear above most page content

## Testing

1. Host the files on your web server
2. Update the URLs in the embed examples to match your domain
3. Add the embed code to your website
4. Test on both desktop and mobile devices

## Troubleshooting

- **Widget not appearing**: Check browser console for errors, verify file URLs are accessible
- **Styling issues**: The widget uses `!important` declarations to maintain consistent appearance
- **Mobile display problems**: Ensure the viewport meta tag is present: `<meta name="viewport" content="width=device-width, initial-scale=1.0">`