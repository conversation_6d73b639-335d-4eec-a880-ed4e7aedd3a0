---
layout: base.html
title: FrontDesk AI - Never Miss a Patient Call Again
---
<!-- Hero Section -->
<section id="home" class="p-4 pt-6 pb-10 flex justify-center items-center">
  <div class="bg-gradient-purple-blue hero-rounded w-full max-w-[1456px] p-8 pb-[250px] relative overflow-hidden">
    <!-- Background Vector Elements -->
    <div class="absolute inset-0 z-0 overflow-hidden">
      <img src="src/assets/images/vector_67_12.svg" alt="" class="absolute top-[22%] right-[-20px] w-full">
      <img src="src/assets/images/vector_67_13.svg" alt="" class="absolute bottom-0 left-[-19%] w-full">
    </div>

    <!-- Navigation -->
    <div class="relative z-10 flex justify-between items-center p-2">
      <!-- Logo & Menu -->
      <div class="flex items-center gap-6">
        <!-- Logo -->
        <div class="flex items-center relative">
          <span class="logo-text">FrontDesk</span>
          <span class="logo-ai logo-ai-sufix">AI</span>
        </div>
        
        <!-- Navigation Links - Desktop -->
        <div class="desktop-nav flex items-center gap-6 ml-6">
          <a href="#solutions" class="nav-link">Solutions</a>
          <a href="#features" class="nav-link">Features</a>
          <a href="#why-frontdesk" class="nav-link">Why FrontDesk</a>
        </div>
      </div>
      
      <!-- Mobile Menu Toggle Button -->
      <button id="mobile-menu-toggle" class="mobile-menu-toggle z-20">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
      
      <!-- CTA Links - Desktop -->
      <div class="desktop-cta flex items-center gap-5">
        <a href="#listen-to-call" class="nav-link text-underline">Listen to a Call</a>
        <a href="https://calendly.com/frontdesk-ai/hello" class="btn btn-primary flex items-center gap-3 button-rounded pl-[1.25rem] pr-[0.5rem]">
          <span>Schedule a Demo</span>
          <div class="bg-green rounded-[17px] p-2 flex items-center justify-center w-[34px] h-[34px]">
            <img src="src/assets/images/arrow_forward.svg" alt="Arrow" class="arrow-forward">
          </div>
        </a>
      </div>
    </div>

    <!-- Mobile Menu - Overlay -->
    <div id="mobile-menu" class="mobile-menu fixed inset-0 bg-gradient-purple-blue z-50 hidden flex-col justify-center items-center">
      <button id="mobile-menu-close" class="mobile-menu-close absolute top-8 right-8">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M6 18L18 6M6 6L18 18" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <div class="flex flex-col items-center gap-8">
        <a href="#solutions" class="mobile-nav-link">Solutions</a>
        <a href="#features" class="mobile-nav-link">Features</a>
        <a href="#why-frontdesk" class="mobile-nav-link">Why FrontDesk</a>
        <a href="#listen-to-call" class="mobile-nav-link">Listen to a Call</a>
        <a href="https://calendly.com/frontdesk-ai/hello" class="btn btn-outline py-2 px-5 button-rounded mt-4">Schedule a Demo</a>
      </div>
    </div>

    <!-- Hero Content -->
    <div class="flex flex-col items-center justify-between mt-40 relative z-10 max-w-[700px] mx-auto">
      <!-- Stars/Sparkle decorations - using exact Figma asset -->
      <div class="absolute left-0 top-[120px] z-10">
        <img src="src/assets/images/star_group.svg" alt="Stars" class="w-[80px]">
      </div>
      <!-- Main Heading -->
      <div class="text-center mb-4 relative">
        <!-- Underline -->
        <img src="src/assets/images/underline.svg" alt="" class="absolute w-[330px] left-[150px] top-[70px] transform -translate-x-1/2 z-5">
        <!-- Arrow pointing to AI status - using exact Figma asset -->
        <div class="absolute top-20 right-[-80px] z-10 transform">
          <img src="src/assets/images/arrow-curled-down.svg" alt="Curved Arrow" class="w-[110px]">
        </div>
        <h1 class="text-white text-[60px] hero-heading relative inline-block leading-tight font-mulish">
          Never Miss a <span class="inline-block font-bold">Patient</span>
          <br><span class="font-bold">Call</span> <img src="src/assets/images/arrow_warm_up.svg" alt="" class="arrow-warmup">
          <span class="inline-block">Again</span>
        </h1>
      </div>

      <!-- CTA Button -->
      <div class="mb-6">
        <a href="https://calendly.com/frontdesk-ai/hello" class="btn btn-outline py-2 px-5 button-rounded">Schedule a Demo</a>
      </div>

      <!-- Call Demo Panel -->
      <div class="w-[450px] space-y-5 mt-6">
        <!-- Phone UI -->
        <div class="bg-black message-box-rounded shadow-lg flex justify-between items-center px-4 py-4 h-[60px] overflow-hidden">
          <div class="flex items-center gap-5">
            <div class="flex items-center justify-center w-6 h-6">
              <img src="src/assets/images/phone_callback.svg" alt="Phone" class="w-5 h-5">
            </div>
            <span class="text-white text-xl font-mulish">FrontDesk</span>
          </div>
          <img src="src/assets/images/waveform.png" alt="Waveform" class="w-[100px] wave-animation">
        </div>
        
        <!-- Message Bubble -->
        <div class="bg-white message-box-rounded shadow-lg px-5 py-4 h-[90px] message-bubble">
          <p id="animated-message" class="text-black font-mulish font-semibold italic text-base leading-relaxed">
            <!-- Text will be inserted by JavaScript -->
          </p>
        </div>
        <!-- Progress bar -->
        <div class="progress-container">
          <div id="progress-bar" class="progress-bar"></div>
        </div>
      </div>
    </div>

    <!-- AI Status - Positioned at bottom right corner to match Figma -->
    <div class="absolute bottom-10 right-6 z-10">
      <div class="bg-transparent-white button-rounded py-1.5 px-5 pr-0 flex items-center gap-2 h-[36px]">
        <span class="text-white font-mulish font-bold text-sm italic">Talking to AI</span>
        <div class="w-[56px] h-[56px] rounded-full border-[3px] border-white overflow-hidden">
          <img src="src/assets/images/avatar.png" alt="AI Assistant" class="w-full h-full object-cover">
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Why Your Practice Needs FrontDesk Section -->
<section id="why-frontdesk" class="p-4 pt-12 pb-12">
  <div class="max-w-[1200px] mx-auto flex flex-col items-center">
    <!-- Blue pill - FrontDesk Solutions -->
    <div class="button-rounded bg-blue-gradient inline-flex items-center justify-center px-5 py-2.5 mb-4">
      <span class="text-white font-mulish font-bold text-sm tracking-widest uppercase">FrontDesk Solutions</span>
    </div>
    
    <!-- Section heading -->
    <h2 class="text-center text-[50px] font-mulish font-semibold leading-tight mb-24 solutions-heading max-w-[500px]">
      Why Your Practice Needs FrontDesk?
    </h2>
    
    <!-- Content container -->
    <div class="flex flex-col lg:flex-row md:gap-20 gap-10 items-center w-full">
      <!-- Left column - Features -->
      <div class="w-full lg:w-5/12 flex flex-col gap-5">
        <h3 class="text-[30px] font-mulish font-semibold feature-heading leading-[120%]">No More Costly And Inefficient Cost Centers</h3>
        <p class="text-gray-600 text-base font-mulish leading-relaxed">
          Traditional front desk operations drain resources and slow down your practice. Our HIPPA-compliant FrontDesk AI automates patient calls, eliminating costly inefficiencies while enhancing the patient experience.
        </p>
        
        <!-- Feature list -->
        <div class="flex flex-col gap-4 mt-2">
          <!-- Feature item 1 -->
          <div class="feature-item">
            <div class="feature-icon">
              <img src="src/assets/images/check_circle.svg" alt="Check" class="w-6 h-6">
            </div>
            <p class="text-gray-600 font-mulish">
              <span class="font-semibold">No More Missed Calls</span> – Every patient inquiry is answered instantly, reducing lost revenue.
            </p>
          </div>
          
          <!-- Feature item 2 -->
          <div class="feature-item">
            <div class="feature-icon">
              <img src="src/assets/images/check_circle.svg" alt="Check" class="w-6 h-6">
            </div>
            <p class="text-gray-600 font-mulish">
              <span class="font-semibold">Reduce Admin Overload</span> – Free up your staff to focus on in-office care, not endless phone calls.
            </p>
          </div>
          
          <!-- Feature item 3 -->
          <div class="feature-item">
            <div class="feature-icon">
              <img src="src/assets/images/check_circle.svg" alt="Check" class="w-6 h-6">
            </div>
            <p class="text-gray-600 font-mulish">
              <span class="font-semibold">Seamless Patient Experience</span> – From scheduling to insurance verification, we handle it all efficiently.
            </p>
          </div>
        </div>
      </div>
      
      <!-- Right column - Chat demo UI -->
      <div class="w-full lg:w-7/12 relative">
        <div class="bg-gradient-purple-blue rounded-[40px] p-6 md:p-12 pt-12 md:pt-[92px] relative" style="min-height: 380px; width: 100%;">
          <!-- Chat UI container -->
          <div class="mx-auto" style="max-width: 100%;">
            <!-- Chat message carousel -->
            <div class="chat-carousel relative mx-auto" style="margin-top: 30px; max-width: 100%;">
              
              <!-- Chat message 1 -->
              <div class="chat-message-container carousel-item active">
                <!-- Header -->
                <div class="bg-black rounded-t-[20px] rounded-b-none flex justify-between items-center px-4 py-2.5 h-[70px]">
                  <div class="flex items-center gap-5">
                    <div class="flex-shrink-0">
                      <img src="src/assets/images/phone_callback.svg" alt="Phone" class="w-5 h-5">
                    </div>
                    <span class="text-white text-xl font-mulish">FrontDesk</span>
                  </div>
                  <img src="src/assets/images/waveform.png" alt="Waveform" class="w-[100px] wave-animation">
                </div>
                <!-- Message body -->
                <div class="bg-white rounded-b-[20px] rounded-t-none md:px-5 px-4 py-4 h-[120px]">
                  <p class="text-black font-mulish font-semibold md:text-lg text-baseleading-relaxed italic">
                    I can help you schedule an appointment. What date and time work best for you?
                  </p>
                </div>
              </div>
              
              <!-- Chat message 2 -->
              <div class="chat-message-container carousel-item">
                <!-- Header -->
                <div class="bg-black rounded-t-[20px] rounded-b-none flex justify-between items-center px-4 py-2.5 h-[70px]">
                  <div class="flex items-center gap-5">
                    <div class="flex-shrink-0">
                      <img src="src/assets/images/phone_callback.svg" alt="Phone" class="w-5 h-5">
                    </div>
                    <span class="text-white text-xl font-mulish">FrontDesk</span>
                  </div>
                  <img src="src/assets/images/waveform.png" alt="Waveform" class="w-[100px] wave-animation">
                </div>
                <!-- Message body -->
                <div class="bg-white rounded-b-[20px] rounded-t-none md:px-5 px-4 py-4 h-[120px]">
                  <p class="text-black font-mulish font-semibold md:text-lg text-base leading-relaxed italic">
                    Let me verify your insurance information. One moment, please.
                  </p>
                </div>
              </div>
              
              <!-- Chat message 3 -->
              <div class="chat-message-container carousel-item">
                <!-- Header -->
                <div class="bg-black rounded-t-[20px] rounded-b-none flex justify-between items-center px-4 py-2.5 h-[70px]">
                  <div class="flex items-center gap-5">
                    <div class="flex-shrink-0">
                      <img src="src/assets/images/phone_callback.svg" alt="Phone" class="w-5 h-5">
                    </div>
                    <span class="text-white text-xl font-mulish">FrontDesk</span>
                  </div>
                  <img src="src/assets/images/waveform.png" alt="Waveform" class="w-[100px] wave-animation">
                </div>
                <!-- Message body -->
                <div class="bg-white rounded-b-[20px] rounded-t-none md:px-5 px-4 py-4 h-[120px]">
                  <p class="text-black font-mulish font-semibold md:text-lg text-base leading-relaxed italic">
                    Your appointment has been confirmed for Thursday at 2:00 PM. We look forward to seeing you.
                  </p>
                </div>
              </div>
            </div>
            
            <!-- Pagination dots -->
            <div class="flex justify-center items-center gap-1 mt-8">
              <span class="pagination-dot active" data-slide="0"></span>
              <span class="pagination-dot" data-slide="1"></span>
              <span class="pagination-dot" data-slide="2"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Calendar Integration Section -->
<section id="solutions" class="p-4 pt-12 pb-12">
  <div class="max-w-[1200px] mx-auto flex flex-col lg:flex-row items-center gap-16">
    <!-- Left Column - Calendar Image with Purple Background -->
    <div class="w-full lg:w-7/12 relative">
      <div class="bg-gradient-purple-blue rounded-[40px] overflow-hidden calendar-container">
        <!-- Calendar image container with rounded corners -->
        <div class="rounded-[20px] bg-white overflow-hidden calendar-image-container p-4">
          <!-- Calendar content -->
          <div class="font-mulish">
            <!-- Calendar header -->
            <div class="calendar-header">
              <p class="calendar-label">Calendar</p>
              <p class="calendar-month">October 2025</p>
            </div>
            
            <!-- Calendar navigation tabs -->
            <div class="calendar-tabs">
              <span class="calendar-tab active">All</span>
              <span class="calendar-tab">Booking</span>
              <span class="calendar-tab">Active</span>
              <span class="calendar-tab">Closed</span>
              <span class="calendar-tab flex items-center"><span class="mr-1">+</span> Filter</span>
            </div>
            
            <!-- Calendar dates -->
            <div class="calendar-dates">
              <div class="calendar-date">
                <p class="calendar-date-number">1</p>
                <p class="calendar-date-day">Mon</p>
              </div>
              <div class="calendar-date">
                <p class="calendar-date-number">2</p>
                <p class="calendar-date-day">Tue</p>
              </div>
              <div class="calendar-date">
                <p class="calendar-date-number">3</p>
                <p class="calendar-date-day">Wed</p>
              </div>
              <div class="calendar-date">
                <p class="calendar-date-number">4</p>
                <p class="calendar-date-day">Thu</p>
              </div>
              <div class="calendar-date">
                <p class="calendar-date-number">5</p>
                <p class="calendar-date-day">Fri</p>
              </div>
            </div>
            
            <!-- Calendar appointments -->
            <div class="calendar-appointments">
              <!-- Time indicators -->
              <div class="calendar-times">
                <p class="calendar-time" style="margin-top: 0;">7:00</p>
                <p class="calendar-time" style="margin-top: 30px;">8:00</p>
                <p class="calendar-time" style="margin-top: 30px;">9:00</p>
              </div>
              
              <!-- Appointment columns -->
              <div class="calendar-columns">
                <!-- Monday column -->
                <div class="calendar-column">
                  <div class="appointment appointment-orange" style="top: 70px; height: 60px;">
                    <p>Dental Cleaning with John Sesme</p>
                    <p class="text-gray-500">John, Jonas</p>
                  </div>
                </div>
                
                <!-- Tuesday column -->
                <div class="calendar-column">
                </div>
                
                <!-- Wednesday column -->
                <div class="calendar-column">
                  <div class="appointment appointment-blue" style="top: 44px; height: 50px;">
                    <p>Jane's Tooth Extraction</p>
                    <span class="appointment-label bg-blue-300">CT</span>
                  </div>
                </div>

                <!-- Thursday column -->
                <div class="calendar-column">
                  <div class="appointment appointment-yellow" style="top: 84px; height: 30px;">
                    <p>Check up</p>
                  </div>
                </div>

                <!-- Friday column -->
                <div class="calendar-column">
                  <div class="appointment appointment-red" style="top: 0; height: 90px;">
                    <p>Dental Cleaning with Forrest</p>
                    <p class="text-gray-500">Headache Surgery Issue</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Right Column - Content -->
    <div class="w-full lg:w-5/12 flex flex-col gap-5">
      <h3 class="text-[30px] font-mulish font-semibold feature-heading calendar-heading leading-[120%]">Seamless Calendar Integration</h3>
      <p class="text-gray-600 text-base font-mulish leading-relaxed">
        FrontDesk integrates with your existing EHR and practice management system, ensuring real-time appointment availability.
      </p>
      
      <!-- CTA Button -->
      <div class="mt-4">
        <a href="https://calendly.com/frontdesk-ai/hello" class="btn-primary flex items-center gap-3 button-rounded pl-[1.25rem] pr-[0.5rem] inline-flex bg-black text-white py-2">
          <span class="font-mulish font-bold text-base">Schedule a Demo</span>
          <div class="bg-green rounded-[17px] p-2 flex items-center justify-center w-[34px] h-[34px]">
            <img src="src/assets/images/arrow_forward.svg" alt="Arrow" class="arrow-forward">
          </div>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Key Features Section -->
<section id="features" class="p-4 pt-12 pb-12">
  <div class="max-w-[1200px] mx-auto">
    <div class="mb-20">
      <div class="button-rounded bg-blue-gradient inline-flex items-center justify-center px-5 py-2.5 mb-5">
        <span class="text-white font-mulish font-bold text-sm tracking-widest uppercase">KEY FEATURES</span>
      </div>
      
      <h3 class="text-[30px] md:text-[50px] font-mulish font-semibold leading-[120%] mb-4">Smart Call Management for<br>Your Practice</h3>
      <p class="text-gray-500 text-base font-mulish leading-relaxed max-w-[700px]">
        Every call matters. Our HIPPA-compliant FrontDesk AI automates and streamlines patient communication, making your practice more efficient.
      </p>
    </div>
    
    <!-- Feature blocks - horizontal layout -->
    <div class="flex flex-wrap justify-start gap-x-3 gap-y-3 mt-4">
      <!-- Feature 1: Appointment Scheduling -->
      <div class="feature-block w-full sm:w-full md:w-[48%] lg:w-[23.5%] bg-gray-100 rounded-[20px] p-6">
        <div class="bg-blue-600 rounded-full w-[54px] h-[54px] flex items-center justify-center mb-5 md:mb-20">
          <img src="src/assets/images/calendar_today.svg" alt="Calendar" class="w-5 h-5">
        </div>
        <h4 class="font-mulish font-semibold text-2xl mb-2">Appointment Scheduling</h4>
        <p class="text-gray-500 font-mulish text-sm">
          Syncs with your calendar, handling new bookings, reschedules, and cancellations effortlessly.
        </p>
      </div>
      
      <!-- Feature 2: Insurance Verification -->
      <div class="feature-block w-full sm:w-full md:w-[48%] lg:w-[23.5%] bg-gray-100 rounded-[20px] p-6">
        <div class="bg-blue-600 rounded-full w-[54px] h-[54px] flex items-center justify-center mb-5 md:mb-20">
          <img src="src/assets/images/fingerprint.svg" alt="Fingerprint" class="w-5 h-5">
        </div>
        <h4 class="font-mulish font-semibold text-2xl mb-2">Insurance Verification</h4>
        <p class="text-gray-500 font-mulish text-sm">
          Ensures accurate patient insurance details before their visit, reducing billing issues.
        </p>
      </div>
      
      <!-- Feature 3: Patient Intake & Onboarding -->
      <div class="feature-block w-full sm:w-full md:w-[48%] lg:w-[23.5%] bg-gray-100 rounded-[20px] p-6">
        <div class="bg-blue-600 rounded-full w-[54px] h-[54px] flex items-center justify-center mb-5 md:mb-20">
          <img src="src/assets/images/group_add.svg" alt="Group Add" class="w-5 h-5">
        </div>
        <h4 class="font-mulish font-semibold text-2xl mb-2">Patient Intake & Onboarding</h4>
        <p class="text-gray-500 font-mulish text-sm">
          Collects essential patient information, streamlining the onboarding process.
        </p>
      </div>
      
      <!-- Feature 4: General Inquiry Handling -->
      <div class="feature-block w-full sm:w-full md:w-[48%] lg:w-[23.5%] bg-gray-100 rounded-[20px] p-6">
        <div class="bg-blue-600 rounded-full w-[54px] h-[54px] flex items-center justify-center mb-5 md:mb-20">
          <img src="src/assets/images/info.svg" alt="Info" class="w-5 h-5">
        </div>
        <h4 class="font-mulish font-semibold text-2xl mb-2">General Inquiry Handling</h4>
        <p class="text-gray-500 font-mulish text-sm">
          Answers common patient questions about services, hours, location, and policies.
        </p>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="flex flex-col md:flex-row justify-between items-center gap-8 mt-10">
      <p class="text-gray-600 font-mulish text-xl leading-relaxed max-w-[560px]">
        Discover how FrontDesk can help your practice run smoothly and enhance patient satisfaction.
      </p>
      <a href="https://calendly.com/frontdesk-ai/hello" class="btn btn-primary flex items-center gap-3 button-rounded pl-[1.25rem] pr-[0.5rem] bg-black text-white py-2 min-w-[214px]">
        <span class="font-mulish font-bold text-base">Book a Free Demo</span>
        <div class="bg-green rounded-[17px] p-2 flex items-center justify-center w-[34px] h-[34px]">
          <img src="src/assets/images/arrow_forward.svg" alt="Arrow" class="arrow-forward">
        </div>
      </a>
    </div>
  </div>
</section>

<!-- Healthcare Provider Section -->
<section class="p-4 pt-12 pb-12">
  <div class="max-w-[1200px] mx-auto">
    <div class="flex flex-col items-center justify-center text-center">
      <!-- Blue pill - KEY FEATURES -->
      <div class="button-rounded bg-blue-gradient inline-flex items-center justify-center px-5 py-2.5 mb-5">
        <span class="text-white font-mulish font-bold text-sm tracking-widest uppercase">KEY FEATURES</span>
      </div>
      
      <!-- Section heading -->
      <h2 class="text-center text-[30px] md:text-[50px] font-mulish font-semibold leading-tight mb-4 max-w-[700px]">
        Any Healthcare Provider<br>That Relies on Patient Calls
      </h2>
      
      <!-- Section description -->
      <p class="text-gray-600 text-base font-mulish leading-relaxed max-w-[700px] mb-8">
        From solo practices to large clinics, Our HIPPA-compliant FrontDesk AI streamlines patient calls, reduces admin tasks, and optimizes scheduling—letting your team focus on care.
      </p>
      
      <!-- Healthcare provider type buttons -->
      <div class="relative w-full max-w-[700px] mb-8">
        <!-- Left fade gradient -->
        <div class="absolute top-0 left-0 h-full w-[25%] bg-gradient-to-r from-white to-transparent z-10 pointer-events-none"></div>
        
        <!-- Right fade gradient -->
        <div class="absolute top-0 right-0 h-full w-[25%] bg-gradient-to-l from-white to-transparent z-10 pointer-events-none"></div>
        
        <!-- Buttons container -->
        <div class="flex flex-col gap-6 w-full overflow-x-hidden">
          <!-- Row 1 -->
          <div class="flex flex-wrap justify-center gap-4">
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Clinics & Medical Practices</button>
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Dentistry & Oral Health</button>
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Therapy & Wellness</button>
          </div>
          
          <!-- Row 2 -->
          <div class="flex flex-wrap justify-center gap-4">
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Hospitals</button>
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Multi-Specialty Centers</button>
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Specialized Clinics</button>
          </div>
          
          <!-- Row 3 -->
          <div class="flex flex-wrap justify-center gap-4">
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Allergy & ENT</button>
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Diagnostic & Imaging Centers</button>
            <button class="button-rounded bg-gray-100 px-5 py-3 text-gray-600 font-mulish font-bold text-base">Fertility Clinics</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Listen to a Call Section -->
<section id="listen-to-call" class="p-4 pt-12 pb-12">
  <div class="max-w-[1200px] mx-auto">
    <div class="rounded-[40px] overflow-hidden relative">
      <div class="bg-gradient-purple-blue rounded-[40px] absolute bottom-0 left-0 right-0 w-full h-[415px]"></div>

      <!-- Person with headset image mobile view -->
      <div class="visible md:invisible absolute bottom-[160px] left-0 right-0">
        <img src="src/assets/images/service-rep-headset.png" alt="Customer Service Representative" class="w-full h-full">
        <!-- fade gradient -->
        <div class="absolute bottom-0 left-0 right-0 w-full h-[60%] bg-gradient-to-t from-[#0F1BA0] to-transparent z-1 pointer-events-none"></div>
      </div>

      <div class="flex flex-col md:flex-row items-end relative z-10">
        <!-- Left Column - Image -->
        <div class="w-full md:w-1/2 relative">
          <!-- Person with headset image -->
          <div class="relative invisible md:visible">
            <img src="src/assets/images/service-rep-headset.png" alt="Customer Service Representative" class="w-full h-full">
            <!-- fade gradient -->
            <div class="absolute bottom-0 left-0 right-0 w-full h-[30%] bg-gradient-to-t from-[#0F1BA0] to-transparent z-1 pointer-events-none"></div>
          </div>
          
          <!-- Front Desk 24/7 Badge -->
          <div class="absolute bottom-10 left-10 p-2 px-5 text-white border-l-2 border-white z-2 invisible md:visible">
            <div class="font-mulish font-bold text-[30px] leading-tight">Front Desk</div>
            <div class="font-mulish font-extrabold text-[50px] leading-none italic">24/7</div>
          </div>
        </div>
        
        <!-- Right Column - Content -->
        <div class="w-full md:w-1/2 p-8 md:p-8 text-white">
          <!-- Blue pill - LISTEN TO A CALL -->
          <div class="button-rounded bg-blue-gradient inline-flex items-center justify-center px-5 py-2.5 mb-2 md:mb-6">
            <span class="text-white font-mulish font-bold text-sm tracking-widest uppercase">LISTEN TO A CALL</span>
          </div>
          
          <!-- Main heading -->
          <h2 class="text-[26px] lg:text-[40px] font-mulish font-semibold leading-tight mb-6 md:mb-8">
            Experience the Future of Patient Communication
          </h2>
          
          <!-- Audio visualization and button -->
          <div class="flex items-center justify-between md:justify-start gap-6 mb-2 md:mb-6 h-[70px] md:h-[80px]">
            <div class="invisible lg:visible">
              <img src="src/assets/images/waveform.png" alt="Audio Waveform" class="w-[120px] lg:w-[180px]">
            </div>

            <!-- Front Desk 24/7 Badge mobile view -->
            <div class="absolute bottom-[30px] md:bottom-10 left-4 p-2 px-3 text-white border-l-2 border-white z-2 visible md:invisible">
              <div class="font-mulish font-bold text-base leading-tight">Front Desk</div>
              <div class="font-mulish font-extrabold text-lg leading-none italic">24/7</div>
            </div>
            
            <!-- Call button -->
            <a href="#" class="button-rounded border-2 border-white flex items-center gap-3 py-2 pr-[0.5rem] pl-[1.25rem] hover:bg-white hover:bg-opacity-10 transition-all min-w-[225px]">
              <span class="text-white font-mulish font-bold">Coming Soon ... </span>
              <div class="bg-green rounded-[17px] p-2 flex items-center justify-center w-[34px] h-[34px]">
                <img src="src/assets/images/arrow_forward.svg" alt="Arrow" class="arrow-forward">
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Footer Section -->
<footer class="bg-white py-8 px-4 max-w-[1456px] mx-auto">
    <!-- Simplified Footer -->
    <div class="flex flex-col md:flex-row justify-between items-center">
      <!-- Logo -->
      <div class="mb-6 md:mb-0">
        <div class="flex items-center relative">
          <span class="logo-text text-black">FrontDesk</span>
          <span class="logo-ai logo-ai-sufix text-black">AI</span>
        </div>
      </div>
      
      <!-- Copyright and Links -->
      <div class="flex items-center gap-4">
        <span class="text-gray-500 font-mulish">© <span id="current-year"></span> FrontDesk</span>
       <!-- <a href="#" class="text-gray-500 font-mulish hover:text-gray-700 transition-all">Privacy</a>
        <a href="#" class="text-gray-500 font-mulish hover:text-gray-700 transition-all">Linkedin</a> -->
      </div>
    </div>
</footer>

<script src="js/main.js"></script> 
<script src="js/carousel.js"></script>