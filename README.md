# FrontDesk Landing Page

## Features

- Responsive design
- Interactive elements with hover effects
- Based on the exact Figma design specifications
- Fast build times with Eleventy (11ty)

## Technologies Used

- HTML5
- Tailwind CSS
- JavaScript
- Eleventy (11ty) for static site generation
- npm for package management

## Installation

1. Clone this repository
2. Install dependencies:

```bash
npm install
```

## Running the Project

Start the development server with:

```bash
npm run dev
```

This will:
- Start an Eleventy dev server with hot-reloading at http://localhost:8080
- Watch for CSS changes and rebuild as needed
- Process templates and build the site automatically

## Building for Production

To build an optimized production version:

```bash
npm run build
```

This command will:
1. Process the HTML templates with Eleventy
2. Generate optimized and minified CSS with Tailwind
3. Copy all necessary assets to the dist folder
4. Fix asset paths and optimize file references
5. Minify HTML, CSS, and JavaScript files
6. Create a ready-to-deploy static website in the `dist` folder

The `dist` folder can then be deployed to any static hosting service (Netlify, Vercel, GitHub Pages, etc.).

## Project Structure

- `index.html` - Main content template with 11ty front matter
- `_includes/` - Template layouts and components
- `.eleventy.js` - Eleventy configuration
- `path-processor.js` - Custom utility to fix asset paths during build
- `src/styles` - CSS styles (Tailwind)
- `src/js` - JavaScript files
- `src/assets/images` - Images and icons

## Build Process Details

The build process uses Eleventy as the main generator with some custom post-processing:

1. **Template Processing**: 11ty processes the templates using Nunjucks
2. **Asset Copying**: Static assets are copied directly to the output directory
3. **CSS Processing**: Tailwind CSS is built and combined with custom CSS
4. **Path Processing**: The custom `path-processor.js` script fixes asset references by removing `src/` prefixes
5. **Minification**: HTML, CSS, and JavaScript are minified for production

The `.eleventy.js` configuration handles these steps through the `eleventy.after` event, ensuring optimal output with correctly referenced assets.

## Build Performance

Using Eleventy significantly improves build performance:
- Faster build times (11ty: ~2s vs other frameworks: ~70s for comparable sites)
- Optimized output with minimal JavaScript
- Efficient asset processing

## Design Implementation Notes

- All colors, spacing, typography, and layout match the Figma design exactly
- All assets are directly extracted from the Figma design
- Interactive elements have hover effects as specified in the design 