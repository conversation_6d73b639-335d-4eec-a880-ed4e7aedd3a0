<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FrontDesk AI - URetina - Demo</title>
        <link rel="stylesheet" href="https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/themes/df-messenger-default.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://www.gstatic.com/dialogflow-console/fast/df-messenger/prod/v1/df-messenger.js"></script>
<style>
        body {
            font-family: 'Mulish', sans-serif;
            background: #ffffff;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .title {
            font-family: 'Mulish', sans-serif;
            color: #333;
            padding: 2rem;
            margin: 0;
            text-align: center;
        }

        /* Enhanced Dialogflow Messenger Styling */
        /* https://cloud.google.com/dialogflow/cx/docs/concept/integration/dialogflow-messenger/css */
        df-messenger {
            z-index: 999;
            position: fixed;
            bottom: 20px;
            right: 20px;
            top: auto;
            left: auto;
            margin: 0 !important;
            --df-messenger-font-family: 'Mulish', sans-serif;
            --df-messenger-font-color: #2c3e50;
            --df-messenger-font-size: 14px;
            /* --df-messenger-chat-border: 1px solid #18163F; */
            --df-messenger-chat-background: #ffffff;
            --df-messenger-message-user-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --df-messenger-message-bot-background: #f8f9fa;
            --df-messenger-message-bot-font-color: #2c3e50;
            --df-messenger-message-user-font-color: #ffffff;
            --df-messenger-chat-border-radius: 20px;
            --df-messenger-chat-bubble-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --df-messenger-chat-bubble-font-color: #ffffff;
            --df-messenger-input-background: #f8f9fa;
            --df-messenger-input-font-color: #2c3e50;
            --df-messenger-input-border-color: #e9ecef;
            --df-messenger-input-placeholder-font-color: #6c757d;
            --df-messenger-titlebar-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --df-messenger-titlebar-font-color: #ffffff;
            --df-messenger-titlebar-title-font-weight: 600;
            --df-messenger-titlebar-icon-font-color: #ffffff;
            --df-messenger-chat-overflow: auto;
            --df-messenger-chat-bubble-icon-color: #ffffff;
        }
    </style>
    <script type="module" crossorigin src="uretina.js"></script>
    <link rel="stylesheet" crossorigin href="uretina.css">
  </head>
  <body>
    <div id="root"></div>
        <df-messenger
      project-id="frontdesk-454309"
      agent-id="edcb5e7c-94dc-4871-9a22-c109f6014f69"
      language-code="en"
      max-query-length="-1">
      <df-messenger-chat-bubble
        chat-title="FrontDesk AI">
      </df-messenger-chat-bubble>
    </df-messenger>
  </body>
</html>
